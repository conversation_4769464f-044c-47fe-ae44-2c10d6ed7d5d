{"name": "next-auth-app", "version": "0.1.1", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" next dev --turbopack", "dev:socket": "node --max-old-space-size=4096 standalone-server.js", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "build:skip-types": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" next build --no-lint --no-types", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" next start", "start:socket": "NODE_ENV=production node --max-old-space-size=4096 standalone-server.js", "lint": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" next lint", "backend": "node --max-old-space-size=4096 backend/server.js", "create-indexes": "node --max-old-space-size=2048 src/scripts/create-indexes.js", "postbuild": "npm run create-indexes", "test:security": "node scripts/test-security-fixes.js"}, "dependencies": {"@auth/core": "^0.34.2", "@aws-sdk/client-s3": "^3.806.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-table": "^8.21.3", "@types/dompurify": "^3.0.5", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.6.0", "emoji-picker-react": "^4.12.2", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "framer-motion": "^12.15.0", "googleapis": "^146.0.0", "gsap": "^3.13.0", "helmet": "^7.1.0", "joi": "^17.11.0", "lucide-react": "^0.475.0", "mongodb": "^6.16.0", "mongoose": "^8.16.1", "motion": "^12.18.1", "next": "^15.3.3", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "ogl": "^1.0.11", "razorpay": "^2.9.6", "react": "latest", "react-dom": "latest", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.4", "resend": "^4.5.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "stripe": "^12.16.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/node": "^20.17.16", "@types/react": "^19.0.8", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "cross-env": "^7.0.3", "daisyui": "^4.12.24", "eslint": "^9.19.0", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}